#!/usr/bin/env python3
"""
ETH Extreme Pressure Demo - Shows actual trading execution
"""

import os
import sys
from decimal import Decimal

sys.path.append('services/the-executioner')
sys.path.append('services/the-ledger')
sys.path.append('services/common')

os.environ['PAPER_TRADING_MODE'] = 'true'

def main():
    print("🚀 PROJECT CHIMERA - ETH EXTREME PRESSURE DEMO")
    print("=" * 60)
    print("Demonstrating ETH trading execution with extreme scenarios...")
    
    try:
        from paper_trading import PaperTradingEngine
        from price_fetcher import get_realtime_price
        
        # Initialize paper trading engine
        engine = PaperTradingEngine()
        print(f"💰 Initial Portfolio: {engine.paper_wallet_balance} ETH, {engine.paper_usdc_balance} USDC")
        
        # Extreme ETH scenarios that would trigger trades
        scenarios = [
            {
                'name': 'ETH Merge Unlock Crisis',
                'unlock_amount': 50000000,  # 50M ETH
                'circulating_supply': 120000000,  # 120M ETH
                'volume_24h': 5000000,   # Only 5M ETH daily volume (low liquidity)
                'description': 'Massive ETH unlock during low liquidity period'
            },
            {
                'name': 'Validator Exit Wave',
                'unlock_amount': 30000000,  # 30M ETH
                'circulating_supply': 120000000,  # 120M ETH
                'volume_24h': 3000000,   # 3M ETH daily volume
                'description': 'Mass validator exits creating unlock pressure'
            }
        ]
        
        print(f"\n🧠 Seer: Analyzing {len(scenarios)} extreme ETH scenarios...")
        
        for i, scenario in enumerate(scenarios):
            print(f"\n{i+1}. {scenario['name']}:")
            print(f"   Description: {scenario['description']}")
            print(f"   Unlock Amount: {scenario['unlock_amount']:,.0f} ETH")
            print(f"   Circulating Supply: {scenario['circulating_supply']:,.0f} ETH")
            print(f"   24h Volume: {scenario['volume_24h']:,.0f} ETH")
            
            # Calculate pressure score
            size_impact = scenario['unlock_amount'] / scenario['circulating_supply']
            liquidity_impact = scenario['unlock_amount'] / scenario['volume_24h']
            pressure_score = size_impact * liquidity_impact
            
            print(f"   Size Impact: {size_impact:.4f}")
            print(f"   Liquidity Impact: {liquidity_impact:.4f}")
            print(f"   Pressure Score: {pressure_score:.4f}")
            
            threshold = 0.50
            if pressure_score >= threshold:
                print(f"   🎯 ETH TRADE SIGNAL: EXTREME PRESSURE (>= {threshold})")
                
                # Execute paper trade
                print(f"   ⚔️ Executioner: Opening ETH short position...")
                
                # Get current ETH price
                try:
                    weth_address = '******************************************'
                    current_price = get_realtime_price(weth_address)
                    if current_price:
                        print(f"   💰 Current ETH Price: ${current_price}")
                    else:
                        current_price = Decimal('3800')
                        print(f"   💰 Using fallback ETH Price: ${current_price}")
                except:
                    current_price = Decimal('3800')
                    print(f"   💰 Using fallback ETH Price: ${current_price}")
                
                borrow_amount = 10  # 10 ETH

                # Create candidate for paper trading
                eth_candidate = {
                    'token_symbol': 'ETH',
                    'contract_address': weth_address,
                    'unlock_amount': scenario['unlock_amount'],
                    'unlock_date': '2024-12-01T00:00:00Z',
                    'pressure_score': pressure_score
                }

                try:
                    position = engine.simulate_position_entry(eth_candidate)
                    print(f"   ✅ SUCCESS: Opened ETH position")
                    print(f"   💵 Entry Price: ${position['entry_price_in_usdc']:.2f}")
                    print(f"   📊 Amount Shorted: {position['amount_shorted']} ETH")
                    print(f"   📊 Updated Portfolio: {engine.paper_wallet_balance} ETH, {engine.paper_usdc_balance} USDC")

                    # Show position details
                    portfolio = engine.get_portfolio_summary()
                    if portfolio['open_positions']:
                        pos = portfolio['open_positions'][-1]
                        print(f"   📈 Position: {pos['amount_shorted']} ETH @ ${pos['entry_price_in_usdc']}")

                        # Simulate risk management
                        print(f"   🛡️ Ledger: Monitoring position for risk management...")
                        entry_price = Decimal(str(pos['entry_price_in_usdc']))

                        # Test stop-loss scenario (15% loss)
                        stop_loss_price = entry_price * Decimal('1.15')
                        print(f"   🚨 Stop-Loss Trigger: ${stop_loss_price}")

                        # Test take-profit scenario (10% profit)
                        take_profit_price = entry_price * Decimal('0.90')
                        print(f"   🎯 Take-Profit Target: ${take_profit_price}")

                except Exception as e:
                    print(f"   ❌ FAILED: {e}")
                    
            else:
                print(f"   ⏸️ ETH SIGNAL: MONITOR (< {threshold})")
        
        # Show final portfolio
        portfolio = engine.get_portfolio_summary()
        print(f"\n📊 Final Portfolio Summary:")
        print(f"   ETH Balance: {portfolio['balances']['eth']}")
        print(f"   USDC Balance: {portfolio['balances']['usdc']}")
        print(f"   Open Positions: {portfolio['positions']['open']}")
        print(f"   Total P&L: ${portfolio['performance']['total_pnl_usd']}")
        
        if portfolio['open_positions']:
            print(f"\n📈 Active ETH Positions:")
            for pos in portfolio['open_positions']:
                print(f"   • {pos['token_symbol']}: {pos['amount_borrowed']} tokens @ ${pos['entry_price_in_usdc']}")
        
        print(f"\n🎉 ETH extreme pressure demo complete!")
        print(f"🚀 System demonstrated successful ETH trading execution!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
